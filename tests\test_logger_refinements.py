"""
测试日志系统的细节优化

测试内容：
1. 新的级别数值（10、20、30、40、50）
2. 新的级别名称（CRIT、WARN）
3. 新的颜色配置
4. 更新的方法名称（crit）
"""

import pytest
from sweeper400.logger import (
    get_logger,
    LogLevel,
    set_debug_mode,
    set_verbose_mode,
    set_brief_mode,
    set_quiet_mode,
    reset_log_levels,
)


class TestRefinedLogLevels:
    """测试优化后的日志级别"""
    
    def test_level_values(self):
        """测试级别数值是否正确"""
        assert LogLevel.DEBUG.value == 10
        assert LogLevel.INFO.value == 20
        assert LogLevel.CRIT.value == 30
        assert LogLevel.WARN.value == 40
        assert LogLevel.ERROR.value == 50
        
        # 测试级别顺序
        assert LogLevel.DEBUG.value < LogLevel.INFO.value
        assert LogLevel.INFO.value < LogLevel.CRIT.value
        assert LogLevel.CRIT.value < LogLevel.WARN.value
        assert LogLevel.WARN.value < LogLevel.ERROR.value
    
    def test_level_names(self):
        """测试级别名称是否正确"""
        # 测试枚举名称
        assert hasattr(LogLevel, 'CRIT')
        assert hasattr(LogLevel, 'WARN')
        
        # 确保旧名称不存在
        assert not hasattr(LogLevel, 'IMPORTANT')
        assert not hasattr(LogLevel, 'WARNING')
    
    def test_crit_method_available(self):
        """测试logger是否有crit方法"""
        logger = get_logger("test.crit")
        assert hasattr(logger, 'crit')
        
        # 测试crit方法可以调用
        try:
            logger.crit("这是一条重要信息")
            assert True
        except Exception as e:
            pytest.fail(f"crit方法调用失败: {e}")
    
    def test_all_refined_levels_work(self):
        """测试所有优化后的日志级别都能正常工作"""
        logger = get_logger("test.refined_levels")
        
        try:
            logger.debug("调试信息")
            logger.info("普通信息")
            logger.crit("重要信息")  # 新的方法名
            logger.warning("警告信息")  # 标准方法仍然可用
            logger.error("错误信息")
            assert True
        except Exception as e:
            pytest.fail(f"优化后的日志级别调用失败: {e}")


class TestRefinedModes:
    """测试优化后的模式函数"""
    
    def setup_method(self):
        """每个测试前重置日志状态"""
        reset_log_levels()
    
    def test_quiet_mode_with_new_levels(self):
        """测试安静模式使用新的WARN级别"""
        set_quiet_mode()
        logger = get_logger("test.quiet_refined")
        
        # 测试所有级别都能正常调用
        try:
            logger.info("普通信息")  # 不应显示
            logger.crit("重要信息")  # 不应显示（CRIT < WARN）
            logger.warning("警告信息")  # 应该显示
            logger.error("错误信息")  # 应该显示
            assert True
        except Exception as e:
            pytest.fail(f"安静模式下日志调用失败: {e}")
    
    def test_brief_mode_with_crit_level(self):
        """测试简要模式处理CRIT级别"""
        set_brief_mode()
        
        # 创建不同层级的logger
        top_logger = get_logger("sweeper400.use.Sweeper")
        sub_logger = get_logger("sweeper400.move.MotorController")
        
        # 测试CRIT级别在简要模式下的行为
        try:
            top_logger.info("顶层信息")
            sub_logger.info("子层信息")  # 可能被过滤
            top_logger.crit("顶层重要信息")  # 应该显示
            sub_logger.crit("子层重要信息")  # 应该显示
            top_logger.warning("顶层警告")  # 应该显示
            sub_logger.warning("子层警告")  # 应该显示
            assert True
        except Exception as e:
            pytest.fail(f"简要模式下CRIT级别调用失败: {e}")


class TestBackwardCompatibility:
    """测试向后兼容性"""
    
    def test_standard_methods_still_work(self):
        """测试标准的logging方法仍然可用"""
        logger = get_logger("test.compat")
        
        try:
            # 标准方法应该仍然可用
            logger.debug("调试")
            logger.info("信息")
            logger.warning("警告")  # 标准warning方法
            logger.error("错误")
            assert True
        except Exception as e:
            pytest.fail(f"标准logging方法失败: {e}")
    
    def test_existing_code_compatibility(self):
        """测试现有代码的兼容性"""
        # 模拟现有代码可能的使用方式
        logger = get_logger("sweeper400.move.MotorController")
        
        try:
            # 这些调用应该仍然正常工作
            logger.info("电机控制器初始化")
            logger.warning("检测到轻微振动")
            logger.error("电机连接失败")
            assert True
        except Exception as e:
            pytest.fail(f"现有代码兼容性测试失败: {e}")


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
