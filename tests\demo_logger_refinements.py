"""
演示日志系统的细节优化

展示：
1. 新的级别数值和名称
2. 新的荧光绿颜色
3. 更简洁的级别名称显示
"""

from sweeper400.logger import (
    get_logger,
    LogLevel,
    set_debug_mode,
    set_verbose_mode,
    set_brief_mode,
    set_quiet_mode,
)

def demo_refined_levels():
    """演示优化后的日志级别"""
    print("\n" + "="*60)
    print("演示优化后的日志级别")
    print("="*60)
    
    logger = get_logger("demo.refined")
    
    print("级别数值：DEBUG(10) → INFO(20) → CRIT(30) → WARN(40) → ERROR(50)")
    print("\n所有日志级别的输出（注意颜色和名称）：")
    
    logger.debug("这是DEBUG级别的信息")
    logger.info("这是INFO级别的信息")
    logger.crit("这是CRIT级别的信息")  # 新的方法名和荧光绿颜色
    logger.warning("这是WARN级别的信息")  # 更短的显示名称
    logger.error("这是ERROR级别的信息")


def demo_color_progression():
    """演示颜色渐变效果"""
    print("\n" + "="*60)
    print("演示颜色渐变效果")
    print("="*60)
    
    logger = get_logger("demo.colors")
    
    print("颜色渐变：青色(DEBUG) → 绿色(INFO) → 荧光绿(CRIT) → 黄色(WARN) → 红色(ERROR)")
    print("观察CRIT级别的荧光绿色如何在绿色和黄色之间形成平滑过渡：")
    
    logger.info("绿色的INFO信息")
    logger.crit("荧光绿的CRIT信息")  # 应该显示为荧光绿
    logger.warning("黄色的WARN信息")


def demo_compact_display():
    """演示更紧凑的显示效果"""
    print("\n" + "="*60)
    print("演示更紧凑的显示效果")
    print("="*60)
    
    logger = get_logger("demo.compact")
    
    print("级别名称长度对比：")
    print("优化前：IMPORTANT(9字符) WARNING(7字符)")
    print("优化后：CRIT(4字符) WARN(4字符)")
    print("\n实际输出效果：")
    
    logger.debug("调试信息")
    logger.info("普通信息")
    logger.crit("重要信息")  # 更短的显示
    logger.warning("警告信息")  # 更短的显示
    logger.error("错误信息")


def demo_real_world_scenario():
    """演示真实场景中的改进效果"""
    print("\n" + "="*60)
    print("演示真实场景中的改进效果")
    print("="*60)
    
    print("场景：扫场测量过程中的日志输出")
    print("注意观察：")
    print("1. CRIT级别的荧光绿色突出显示重要信息")
    print("2. 更紧凑的级别名称节省屏幕空间")
    print("3. 平滑的颜色过渡提供更好的视觉体验")
    
    set_verbose_mode()
    
    # 模拟扫场测量过程
    sweeper_logger = get_logger("sweeper400.use.Sweeper")
    motor_logger = get_logger("sweeper400.move.MotorController")
    measure_logger = get_logger("sweeper400.measure.HiPerfCSSIO")
    
    print("\n开始扫场测量...")
    sweeper_logger.info("初始化扫场测量器")
    motor_logger.info("初始化电机控制器")
    measure_logger.info("初始化数据采集系统")
    
    # 重要的状态信息（使用CRIT级别）
    sweeper_logger.crit("扫场测量器就绪")  # 荧光绿突出显示
    motor_logger.crit("电机校准完成")
    measure_logger.crit("数据采集系统就绪")
    
    # 警告信息
    motor_logger.warning("检测到轻微振动")
    measure_logger.warning("数据采集速度较慢")
    
    # 完成信息
    sweeper_logger.crit("扫场测量完成")  # 荧光绿突出显示重要完成状态


def demo_brief_mode_improvements():
    """演示简要模式的改进"""
    print("\n" + "="*60)
    print("演示简要模式的改进")
    print("="*60)
    
    print("简要模式：只显示顶层INFO + 所有CRIT/WARN/ERROR")
    
    set_brief_mode()
    
    # 创建不同层级的logger
    sweeper_logger = get_logger("sweeper400.use.Sweeper")
    motor_logger = get_logger("sweeper400.move.MotorController")
    measure_logger = get_logger("sweeper400.measure.HiPerfCSSIO")
    
    print("\n简要模式输出：")
    sweeper_logger.info("Sweeper: 开始测量")  # 顶层INFO，应该显示
    motor_logger.info("MotorController: 移动中")  # 子层INFO，可能被过滤
    measure_logger.info("HiPerfCSSIO: 采集中")  # 子层INFO，可能被过滤
    
    # CRIT级别总是显示（荧光绿突出）
    sweeper_logger.crit("Sweeper: 关键状态更新")
    motor_logger.crit("MotorController: 位置到达")
    measure_logger.crit("HiPerfCSSIO: 数据就绪")
    
    # WARN和ERROR总是显示
    motor_logger.warning("MotorController: 速度调整")
    measure_logger.warning("HiPerfCSSIO: 缓冲区满")


if __name__ == "__main__":
    print("sweeper400 日志系统细节优化演示")
    print("="*60)
    print("优化内容：")
    print("1. 级别数值：10、20、30、40、50（更直观）")
    print("2. 级别名称：CRIT、WARN（更紧凑）")
    print("3. 颜色优化：荧光绿CRIT级别（平滑过渡）")
    
    # 演示各项优化
    demo_refined_levels()
    demo_color_progression()
    demo_compact_display()
    demo_real_world_scenario()
    demo_brief_mode_improvements()
    
    print("\n" + "="*60)
    print("优化演示完成！")
    print("主要改进：")
    print("✓ 更直观的级别数值（10的倍数）")
    print("✓ 更紧凑的级别名称（节省4-5个字符）")
    print("✓ 更和谐的颜色过渡（荧光绿）")
    print("✓ 保持完全的向后兼容性")
    print("="*60)
