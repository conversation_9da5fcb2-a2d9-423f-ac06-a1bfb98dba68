# 使用
# flet run -r flet_test.py
# 其中“-r”表示自动重载模式，可自动应用新更改，方便调试。

import flet as ft


def main(page: ft.Page):
    # 1. 配置页面/窗口
    page.title = "Flet 计数器应用"
    page.vertical_alignment = ft.MainAxisAlignment.CENTER  # 垂直居中

    # 2. 创建控件
    # 一个不可编辑的文本输入框，用于显示数字
    txt_number = ft.TextField(value="0", text_align=ft.TextAlign.RIGHT, width=100)

    # 3. 定义事件处理器 (函数)
    def minus_click(e):
        # 将文本框的值转为整数，减 1，再转回字符串
        current_value = int(txt_number.value)
        txt_number.value = str(current_value - 1)
        # 调用 page.update() 来将更改发送到前端
        page.update()

    def plus_click(e):
        current_value = int(txt_number.value)
        txt_number.value = str(current_value + 1)
        # 这里我们也可以只更新单个控件，效率更高
        # txt_number.update()  # <-- 如果页面上只有这一个控件变化，这样更好
        page.update()  # 为了简单，我们先用 page.update()

    # 4. 组装 UI
    # 将减号按钮、数字显示框、加号按钮放入一个水平行 (Row) 中
    page.add(
        ft.Row(
            [
                ft.IconButton(ft.Icons.REMOVE, on_click=minus_click, tooltip="减少"),
                txt_number,
                ft.IconButton(ft.Icons.ADD, on_click=plus_click, tooltip="增加"),
            ],
            alignment=ft.MainAxisAlignment.CENTER,  # 水平居中
        )
    )


# 5. 启动应用
# target=main 表示应用启动时调用 main 函数
ft.app(target=main)

# 如果你想以网页形式运行，可以这样写：
# ft.app(target=main, view=ft.AppView.WEB_BROWSER)
