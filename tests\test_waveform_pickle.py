# pyright: basic
import pickle
import numpy as np
from sweeper400.analyze import Waveform

# 创建一个简单的Waveform对象
data = np.array([1.0, 2.0, 3.0, 4.0])
waveform = Waveform(data, sampling_rate=1000.0)

print(f"原始波形: {waveform}")
print(f"原始采样率: {waveform.sampling_rate}")
print(f"原始_sampling_rate属性: {hasattr(waveform, '_sampling_rate')}")

# 序列化
serialized = pickle.dumps(waveform)
print(f"序列化成功，大小: {len(serialized)} 字节")

# 反序列化
try:
    restored_waveform = pickle.loads(serialized)
    print(f"反序列化成功: {restored_waveform}")
    print(f"恢复的采样率: {restored_waveform.sampling_rate}")
    print(f"恢复的_sampling_rate属性: {hasattr(restored_waveform, '_sampling_rate')}")
    if hasattr(restored_waveform, '_sampling_rate'):
        print(f"_sampling_rate值: {restored_waveform._sampling_rate}")
except Exception as e:
    print(f"反序列化失败: {e}")
    import traceback
    traceback.print_exc()
