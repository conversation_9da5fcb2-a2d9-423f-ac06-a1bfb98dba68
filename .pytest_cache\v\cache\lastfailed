{"tests/test_synchronized_ai_ao_measurement.py::TestSynchronizedAiAoMeasurement::test_basic_measurement": true, "tests/test_continuous_acquisition.py": true, "tests/test_simple_continuous.py": true, "tests/test_package_import.py::test_package_level_import": true, "tests/test_cont_sync_sine_aiao.py::TestContSyncSineAIAO": true, "tests/test_get_sine_cycles.py": true, "tests/test_sweeper.py::TestSweeper": true, "tests/test_logger_improvements.py::TestNewLogLevel::test_important_method_available": true, "tests/test_logger_improvements.py::TestSmartBriefMode::test_verbose_mode_shows_all_info": true, "tests/test_logger_improvements.py::TestSmartBriefMode::test_brief_mode_filters_sub_info": true, "tests/test_logger_improvements.py::TestSmartBriefMode::test_brief_mode_shows_all_important_and_above": true, "tests/test_logger_improvements.py::TestConvenienceModes::test_debug_mode": true, "tests/test_logger_improvements.py::TestConvenienceModes::test_quiet_mode": true, "tests/test_logger_improvements.py::TestConvenienceModes::test_backward_compatibility": true}