"""
演示新的日志系统功能

展示新的IMPORTANT级别、智能简要模式和便捷模式函数
"""

from sweeper400.logger import (
    get_logger,
    set_debug_mode,
    set_verbose_mode,
    set_brief_mode,
    set_quiet_mode,
)

def demo_new_important_level():
    """演示新的IMPORTANT级别"""
    print("\n" + "="*60)
    print("演示新的IMPORTANT级别")
    print("="*60)
    
    logger = get_logger("demo.important")
    
    print("所有日志级别的输出：")
    logger.debug("这是DEBUG级别的信息")
    logger.info("这是INFO级别的信息")
    logger.important("这是IMPORTANT级别的信息")  # 新增的级别
    logger.warning("这是WARNING级别的信息")
    logger.error("这是ERROR级别的信息")


def demo_smart_brief_mode():
    """演示智能简要模式"""
    print("\n" + "="*60)
    print("演示智能简要模式")
    print("="*60)
    
    # 创建不同层级的logger
    sweeper_logger = get_logger("sweeper400.use.Sweeper")
    motor_logger = get_logger("sweeper400.move.MotorController")
    measure_logger = get_logger("sweeper400.measure.HiPerfCSSIO")
    
    print("\n1. 详细模式（显示所有INFO及以上级别）：")
    set_verbose_mode()
    sweeper_logger.info("Sweeper: 开始扫场测量")
    motor_logger.info("MotorController: 移动到位置 (10, 20)")
    measure_logger.info("HiPerfCSSIO: 开始数据采集")
    sweeper_logger.important("Sweeper: 测量完成")
    
    print("\n2. 简要模式（只显示顶层INFO + 所有IMPORTANT/WARNING/ERROR）：")
    set_brief_mode()
    sweeper_logger.info("Sweeper: 开始扫场测量")
    motor_logger.info("MotorController: 移动到位置 (10, 20)")  # 可能被过滤
    measure_logger.info("HiPerfCSSIO: 开始数据采集")  # 可能被过滤
    sweeper_logger.important("Sweeper: 测量完成")  # 总是显示
    motor_logger.important("MotorController: 电机状态正常")  # 总是显示
    measure_logger.warning("HiPerfCSSIO: 数据采集速度较慢")  # 总是显示


def demo_convenience_modes():
    """演示便捷模式函数"""
    print("\n" + "="*60)
    print("演示便捷模式函数")
    print("="*60)
    
    logger = get_logger("demo.modes")
    
    print("\n1. 调试模式（显示所有日志）：")
    set_debug_mode()
    logger.debug("调试信息")
    logger.info("普通信息")
    logger.important("重要信息")
    logger.warning("警告信息")
    
    print("\n2. 详细模式（显示INFO及以上，新的默认模式）：")
    set_verbose_mode()
    logger.debug("调试信息")  # 不显示
    logger.info("普通信息")
    logger.important("重要信息")
    logger.warning("警告信息")
    
    print("\n3. 简要模式（智能过滤INFO，显示所有IMPORTANT及以上）：")
    set_brief_mode()
    logger.debug("调试信息")  # 不显示
    logger.info("普通信息")  # 可能被过滤
    logger.important("重要信息")
    logger.warning("警告信息")
    
    print("\n4. 安静模式（只显示WARNING及以上）：")
    set_quiet_mode()
    logger.debug("调试信息")  # 不显示
    logger.info("普通信息")  # 不显示
    logger.important("重要信息")  # 不显示
    logger.warning("警告信息")
    logger.error("错误信息")


def demo_real_world_scenario():
    """演示真实世界的使用场景"""
    print("\n" + "="*60)
    print("演示真实世界的使用场景")
    print("="*60)
    
    # 模拟使用Sweeper类的场景
    print("\n场景：用户直接使用Sweeper进行扫场测量")
    print("期望：在简要模式下，只看到Sweeper的关键信息，不被子组件的详细信息干扰")
    
    set_brief_mode()
    
    # 模拟Sweeper的使用
    sweeper_logger = get_logger("sweeper400.use.Sweeper")
    motor_logger = get_logger("sweeper400.move.MotorController")
    measure_logger = get_logger("sweeper400.measure.HiPerfCSSIO")
    
    print("\n开始扫场测量...")
    sweeper_logger.info("初始化扫场测量器")
    sweeper_logger.info("点阵包含 25 个测量点")
    
    # 子组件的详细信息（在简要模式下可能被过滤）
    motor_logger.info("初始化电机控制器")
    motor_logger.info("设置电机参数：步进角度=1.8°, 细分数=20")
    measure_logger.info("初始化数据采集系统")
    measure_logger.info("配置采样率：100kHz")
    
    # 重要信息（总是显示）
    sweeper_logger.important("开始扫场测量")
    motor_logger.important("电机校准完成")
    measure_logger.important("数据采集系统就绪")
    
    # 警告和错误（总是显示）
    motor_logger.warning("检测到轻微振动")
    sweeper_logger.important("扫场测量完成")


if __name__ == "__main__":
    print("sweeper400 日志系统改进功能演示")
    print("="*60)
    
    # 演示各项新功能
    demo_new_important_level()
    demo_smart_brief_mode()
    demo_convenience_modes()
    demo_real_world_scenario()
    
    print("\n" + "="*60)
    print("演示完成！")
    print("="*60)
